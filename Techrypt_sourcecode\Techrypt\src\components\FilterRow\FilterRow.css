.row {
  position: relative; /* To position the image relative to the row */
  width: 100%;
  border-top: 2px solid white;
  padding-bottom: 25px;
  padding: 30px 0 22px 0;
  color: white; /* Ensure text is white by default */
}

.visible {
  display: flex;
}

.rowsame {
  width: 32%;
  font-size: 20px;
  font-weight: 300;
  color: white; /* Ensure client details text is white */
}

.showbtn {
  width: 4%;
}

.show-icon {
  color: white;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 2rem;
}

.show-icon:hover {
  background: rgba(174, 187, 30, 0.2);
  transform: scale(1.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.row:hover {
  color: white;
}

.expanded {
  display: flex;
}


.collapsed {
  display: none;
}

h6 {
  color: #444;
  font-size: 14px;
}

h4 {
  font-weight: 400;
  color: white; /* Ensure expanded details text is white */
}

/* Mobile-Specific Responsive Rules */
@media (max-width: 768px) {
  .row {
    padding: 16px;
  }

  .visible {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .rowsame {
    font-size: 14px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .rowsame:last-of-type {
    border-bottom: none;
  }

  .showbtn {
    align-self: center;
    margin-top: 8px;
  }

  .show-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
    padding: 8px;
    background: rgba(174, 187, 30, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    min-width: 44px; /* Touch-friendly minimum size */
    min-height: 44px;
  }

  .show-icon:hover {
    background: rgba(174, 187, 30, 0.2);
    transform: scale(1.1);
  }

  .hover-image {
    display: none; /* Hide hover image on mobile for better UX */
  }
}

/* Tablet Responsive Rules */
@media (min-width: 769px) and (max-width: 1024px) {
  .rowsame {
    font-size: 18px;
  }

  .show-icon {
    width: 42px;
    height: 42px;
    font-size: 1.4rem;
  }
}

/* Desktop Responsive Rules */
@media (min-width: 1025px) {
  .rowsame {
    font-size: 20px;
  }

  .show-icon {
    width: 44px;
    height: 44px;
    font-size: 1.5rem;
  }
}

/* Enhanced Expanded Section Styles */
.expanded {
  max-height: 1000px;
  opacity: 1;
  transition: all 0.4s ease-in-out;
  overflow: visible;
}

.collapsed {
  max-height: 0;
  opacity: 0;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
}

/* Mobile Expanded Section */
@media (max-width: 768px) {
  .expanded {
    padding: 16px;
  }

  .row-mid-subs {
    margin-bottom: 16px;
  }

  .row-mid-subs h6 {
    font-size: 12px;
    margin-bottom: 4px;
  }

  .row-mid-subs h4 {
    font-size: 14px;
    line-height: 1.4;
  }
}

.row-left > img {
  width: 80%;
  height: fit-content;
}

/* Hidden image on collapsed row */
.hover-image {
  display: none;
  position: absolute;
  top: -150px; /* Adjust the positioning above the row */
  left: 50%;
  width: 50vw; /* Adjust the size of the image */
  height: auto;
  z-index: 10;
}
.hover-image img {
  width: 100%;
  height: fit-content;
}

/* Show the image on hover only when the row is collapsed and not clicked */
.row:not(.expanded):hover .hover-image {
  display: block;
}

/* Hide hover image when the row is clicked (expanded) */
.hide-hover {
  display: none !important;
}

@media only screen and (max-width: 600px) {
  .rowsame {
    font-size: 1rem;
  }
}
