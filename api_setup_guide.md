# 🚀 Intelligent API Integration Setup Guide

## **Quick Setup for Enhanced Techrypt Chatbot**

This guide helps you set up free intelligent APIs to enhance your Techrypt chatbot with sub-3-second response times and business-specific intelligence.

## **🏆 RECOMMENDED APIS (FREE TIER)**

### **1. Groq API (PRIMARY - FASTEST)**
- ⚡ **Speed**: Sub-1-second responses
- 🆓 **Free Tier**: 14,400 requests/day
- 🧠 **Models**: Llama 3.1, <PERSON><PERSON>, Gemma

**Setup Steps:**
1. Visit: https://console.groq.com/
2. Sign up with email
3. Go to API Keys section
4. Create new API key
5. Copy the key

### **2. Together AI (FALLBACK - RELIABLE)**
- ⚡ **Speed**: 1-2 second responses
- 🆓 **Free Tier**: $25 credits monthly
- 🧠 **Models**: Llama 3.1, Mistral, Code Llama

**Setup Steps:**
1. Visit: https://api.together.xyz/
2. Sign up with email
3. Go to API Keys section
4. Create new API key
5. Copy the key

### **3. Hugging Face (BACKUP - OPTIONAL)**
- ⚡ **Speed**: 2-3 second responses
- 🆓 **Free Tier**: Rate-limited but free
- 🧠 **Models**: Various open-source models

**Setup Steps:**
1. Visit: https://huggingface.co/
2. Sign up with email
3. Go to Settings > Access Tokens
4. Create new token with "Read" permissions
5. Copy the token

## **🔧 CONFIGURATION**

### **Method 1: Environment Variables (Recommended)**

Create a `.env` file in your project root:

```bash
# Intelligent API Configuration
GROQ_API_KEY=your_groq_api_key_here
TOGETHER_API_KEY=your_together_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_token_here

# Enable API Integration
USE_INTELLIGENT_APIS=True
INTELLIGENT_MODE=True
```

### **Method 2: PowerShell Environment Variables**

```powershell
# Set API keys for current session
$env:GROQ_API_KEY = "your_groq_api_key_here"
$env:TOGETHER_API_KEY = "your_together_api_key_here"
$env:HUGGINGFACE_API_KEY = "your_huggingface_token_here"
$env:USE_INTELLIGENT_APIS = "True"
$env:INTELLIGENT_MODE = "True"
```

### **Method 3: System Environment Variables**

**Windows:**
1. Open System Properties > Environment Variables
2. Add new variables:
   - `GROQ_API_KEY` = your_groq_api_key
   - `TOGETHER_API_KEY` = your_together_api_key
   - `HUGGINGFACE_API_KEY` = your_huggingface_token
   - `USE_INTELLIGENT_APIS` = True

## **📊 EXPECTED PERFORMANCE**

With API integration enabled:

- **Response Time**: 0.5-2 seconds (vs 6-8 seconds with TinyLLaMA)
- **Success Rate**: 95%+ intelligent responses
- **Business Context**: Industry-specific recommendations
- **Fallback Chain**: API → CSV → Rule-based
- **Cost**: $0 (within free tier limits)

## **🔍 TESTING YOUR SETUP**

Run the test script to verify API integration:

```bash
python intelligent_api_integration.py
```

Expected output:
```
✅ Groq API key found
✅ Together AI API key found
✅ Hugging Face API key found
🔍 Testing: How will a website help my restaurant? (restaurant)
✅ groq_api: For a restaurant, a website serves as your digital storefront...
   Time: 0.847s
```

## **🚨 TROUBLESHOOTING**

### **Common Issues:**

**1. "API key not found" error:**
- Verify environment variables are set correctly
- Restart your terminal/IDE after setting variables
- Check for typos in variable names

**2. "API request failed" error:**
- Verify API keys are valid and active
- Check internet connection
- Ensure you haven't exceeded free tier limits

**3. "Slow responses" issue:**
- Check if you're using the correct API endpoints
- Verify network connectivity
- Consider switching API priority order

### **Fallback Behavior:**

The system automatically falls back in this order:
1. **Groq API** (fastest)
2. **Together AI** (reliable)
3. **Hugging Face** (backup)
4. **CSV responses** (existing system)
5. **Rule-based responses** (existing system)

## **💡 OPTIMIZATION TIPS**

### **For Best Performance:**

1. **Use Groq as Primary**: Fastest response times
2. **Set Together AI as Fallback**: Good reliability
3. **Monitor Usage**: Track API calls to stay within limits
4. **Cache Responses**: System automatically caches similar queries

### **Free Tier Limits:**

- **Groq**: 14,400 requests/day (600/hour)
- **Together AI**: $25 credits (~5,000-10,000 requests
- **Hugging Face**: Rate-limited but generous

### **Production Recommendations:**

1. **Monitor API usage** through provider dashboards
2. **Set up billing alerts** if you approach limits
3. **Consider upgrading** to paid tiers for high-volume usage
4. **Implement request queuing** for peak traffic

## **🎯 BUSINESS BENEFITS**

With intelligent API integration:

### **For Restaurants:**
- "A website will help your restaurant by showcasing your menu with appetizing photos, enabling online ordering for takeout and delivery, and building customer trust through reviews and testimonials."

### **For Retail Businesses:**
- "An e-commerce website will help your electrical appliances shop by displaying your product catalog with detailed specifications, enabling secure online payments, and reaching customers beyond your local area."

### **For Professional Services:**
- "A professional website will help your law practice by establishing credibility with potential clients, showcasing your expertise and case results, and providing easy consultation booking."

## **🔄 MIGRATION FROM TINYLLAMA**

The new API integration:
- ✅ **Preserves all existing functionality**
- ✅ **Maintains CSV response system**
- ✅ **Keeps conversation context**
- ✅ **Retains appointment booking**
- ✅ **Improves response speed by 3-4x**
- ✅ **Increases response quality significantly**

## **📈 MONITORING & ANALYTICS**

Use the enhanced statistics method:

```python
chatbot = IntelligentLLMChatbot()
stats = chatbot.get_enhanced_statistics()
print(stats['api_stats'])
```

This provides:
- API success rates
- Average response times
- Cache utilization
- Fallback usage patterns

## **🎉 READY TO DEPLOY**

Once configured, your Techrypt chatbot will provide:
- ⚡ Sub-3-second intelligent responses
- 🧠 Business-specific recommendations
- 🔄 Reliable fallback system
- 📊 Comprehensive analytics
- 💰 Cost-effective operation

Your chatbot is now ready for production with enhanced intelligence!
