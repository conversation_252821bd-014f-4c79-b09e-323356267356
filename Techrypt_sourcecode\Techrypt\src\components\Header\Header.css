* {
  margin: 0;
  padding: 0;
}
/* Desktop Navbar - Compact Design */
.navbar {
  padding: 0px 8px; /* Reduced padding */
  border-radius: 0px 0px 16px 16px; /* Slightly smaller border radius */
  display: flex;
  align-items: center;
  gap: 15px; /* Reduced gap */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  background-color: #000000 !important;
  height: 65px; /* Reduced overall height */
}

.leftNav,
.rightNav {
  display: flex;
  gap: 40px; /* Reduced gap */
  align-items: center;
}
.leftNav{
  height: 65px; /* Reduced height to match navbar */
  display: flex;
  align-items: center; /* Center logo vertically */
  flex-shrink: 0; /* Prevent shrinking */
  overflow: visible; /* Allow logo to display fully */
}
.leftNav 
.hr1,
.hr2 {
  color: white;
  width: 100px;
}

/* Compact Desktop Navigation */
.midNav {
  display: flex;
  /*background-color: #AEBB1E !important;*/
  background-color: #C4D322;
  border-radius: 25px; /* Slightly smaller border radius */
  height: 42px; /* Reduced height */
  align-items: center;
  justify-content: center;
  width: fit-content;
  padding: 4px 12px; /* Reduced padding */
}
@media only screen and (max-width: 768px) {
  .midNav {
    display: none;
  }
}


.navList {
  display: flex;
  /* padding: 0px 10px; */
  border-radius: 30px;
  transition: scale 12s ease-in-out;
}

.navList li a {
  border-radius: 10px;
  color: var(--color-black);

  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease; /* Apply smooth transition */
}

.navList li a:active {
  border-radius: 10px;
}

/* Compact Navigation Items */
.listItems {
  font-size: 18px; /* Reduced font size */
  font-weight: bold;
  line-height: 18px; /* Reduced line height */
  list-style-type: none;
  margin-right: 8px; /* Reduced margin */
  margin-left: 8px; /* Reduced margin */

  &:hover {
    cursor: pointer;
    border-radius: 12px; /* Slightly smaller border radius */
  }
}

.dropDownAnchor {
  margin-left: 30px;
}

.anchor {
  color: rgb(15, 15, 15);
  text-decoration: none;
}

.icon {
  cursor: pointer;
  /* height: 120px; */
  object-fit: cover;
}

/* Compact Desktop Header Logo - Reduced size for compact header */
.desktop-header-logo {
  width: 240px !important; /* Reduced from 280px */
  height: auto !important;
  object-fit: cover !important;
  display: block !important;
  flex-shrink: 0 !important;
  max-width: none !important;
}

/* Responsive breakpoints for desktop logo - proportionally reduced sizing */
@media (min-width: 768px) and (max-width: 1023px) {
  .desktop-header-logo {
    width: 240px !important; /* Reduced from 280px */
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .desktop-header-logo {
    width: 275px !important; /* Reduced from 320px */
  }
}

@media (min-width: 1280px) {
  .desktop-header-logo {
    width: 300px !important; /* Reduced from 350px */
  }
}
/* Dropdown initially hidden - Adjusted for compact header */
.dropdown {
  position: absolute;
  max-width: 1200px;
  top: 4.1rem; /* Reduced from 4.7rem to match compact header */
  background-color: var(--color-black);
  border-radius: 5px;
  z-index: 100;
  right: 0;
  left: 0;
  width: 99vw;
  height: 90vh;
  border-bottom: 2px solid white;
  display: flex;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
  opacity: 0;
  transform: translateY(-100%); /* Move it off-screen at the start */
  visibility: hidden;
}

/* Keyframe for slide down from top to bottom */
@keyframes dropdownSlideIn {
  0% {
    transform: translateY(-100%); /* Start off-screen */
    opacity: 0;
  }
  100% {
    transform: translateY(0); /* Slide to its original position */
    opacity: 1;
  }
}

/* Keyframe for slide up to hide */
@keyframes dropdownSlideOut {
  0% {
    transform: translateY(0); /* Start in the visible position */
    opacity: 1;
  }
  100% {
    transform: translateY(-100%); /* Slide it off-screen */
    opacity: 0;
  }
}

/* When dropdown is visible, apply the slide down animation */
.dropdown-visible {
  animation: dropdownSlideIn 0.6s ease forwards; /* Slide down */
  visibility: visible;
}

/* When dropdown is hidden, apply the slide up animation */
.dropdown-hidden {
  animation: dropdownSlideOut 0.6s ease forwards; /* Slide up */
  visibility: hidden;
}

.yellowDiv {
  display: flex;
  height: 100px;
  align-items: center;
  justify-content: center;
  width: 60rem;
  border-radius: 30px;
  margin-bottom: 40px;
}

.dropDownList {
  font-family: "Right Grotesk", sans-serif;
  font-size: 50px;
  font-weight: 500;
  line-height: 54px;
  list-style-type: none;
  color: var(--color-black);
/* 
  &:focus {
    background-color: var(--color-black);
    color: white;
  } */
}

.inputDiv {
  display: flex;
  gap: 20px;
}

.leftInput,
.rightInput {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.input,
.Secondinput {
  width: 23rem;
  border: none;
  outline: none;
  font-size: 22px;
  border-bottom: 2px solid white;
  color: white;
  background-color: var(--color-black);
  padding-left: 2px;
}

.Secondinput {
  width: 47.4rem;
  margin-top: 20px;
}

.submit {
  margin-top: 20px;
  padding: 10px 20px;
  border: none;
  font-size: 22px;
  font-weight: bold;
  border-radius: 40px;

  &:hover {
    cursor: pointer;
    background-color: var(--color-yellow);
  }
}

.button {
  display: flex;
  flex-direction: column;
}

.footerText {
  margin-top: 10px;
  line-height: 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.3);
  width: 370px;
  text-align: center;
}
.underline{
  text-decoration: underline;

}
.footer-div{
  padding: 0px 25px;
}
/* Compact Navigation Button */
.navButton {
  border: none;
  font-size: 16px; /* Reduced font size */
  margin: 2px;
  padding: 6px 14px; /* Reduced padding */
  border-radius: 20px; /* Smaller border radius */
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease, border-radius 0.3s ease;
  background-color: transparent;
  color: #000000;
  text-decoration: none;
  display: block;
}

.navButton:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: #000000;
  border-radius: 20px; /* Updated to match new border radius */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-0.5px);
}

.navButton.active:hover {
  background-color: #ffffff;
  color: #C4D322;
  box-shadow: 0 4px 12px rgba(174, 187, 30, 0.3);
  transform: translateY(-1px);
}

.navButton.active {
  background-color: #ffffff;
  /*color: #AEBB1E;*/
  color: #9cad25;
  border-radius: 20px; /* Updated to match new border radius */
  box-shadow: 0 2px 8px rgba(174, 187, 30, 0.2);
  transform: translateY(-1px);
}



/* RESPONSIVE HEADER VISIBILITY CONTROLS */
/* Desktop navigation - Only visible on screens >768px */
@media only screen and (min-width: 769px) {
  .navbar {
    display: flex !important; /* Show compact desktop navbar */
  }

  .main-tabs {
    display: flex !important;
  }

  .main-tab {
    display: block !important;
  }
}

/* Mobile navigation - Only visible on screens ≤768px */
@media only screen and (max-width: 768px) {
  .navbar {
    display: none !important; /* Hide desktop navbar on mobile */
  }

  /* Hide desktop navigation elements on mobile only */
  .main-tabs,
  .main-tab {
    display: none !important;
  }
}

.small-nav {
  display: flex;
  justify-content: space-between;
  text-align: center;
  align-items: center;
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #000000 !important;
}

.linehor {
  background: white;
  height: 2px;
  width: 160px;
}

/* Mobile Navigation Styles - Hidden since we use hamburger menu */
.mobile-nav-tabs {
  display: none !important; /* Force hide the old mobile tabs */
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  z-index: 998;
  background-color: #0f0f0f;
  padding: 10px;
}

.mobile-nav-container {
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-tabs-wrapper {
  display: flex;
  justify-content: center;
  background-color: #C4D322;
  border-radius: 25px;
  padding: 5px;
  gap: 5px;
}

.mobile-tab-button {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  color: #000000;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  background-color: transparent;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.mobile-tab-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.mobile-tab-button.mobile-tab-active {
  background-color: #ffffff;
  /*color: #AEBB1E;*/
  color: #C4D322;
}

@media only screen and (max-width: 768px) {
  .mobile-nav-tabs {
    display: none !important; /* Keep hidden - using hamburger menu instead */
  }
}

/* Hide mobile navigation on desktop screens >768px */
@media only screen and (min-width: 769px) {
  .small-nav {
    display: none !important; /* Hide mobile navbar on desktop */
  }
  .small-main {
    display: none !important;
  }
  .mobile-nav-tabs {
    display: none !important;
  }
}

.dropdown-mobile {
  background: var(--color-black);
  position: absolute;
  top: 160px; /* Adjust as per your header height */
  left: 0;
  right: 0;
  padding: 20px;
  z-index: 10;
  border-radius: 8px;
  height: auto;
}

.dropdown-mobile .navList {
  text-align: start;
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
  background: var(--color-yellow);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.dropdown-mobile .navList .dropDownList {
  margin-bottom: 10px;
}

.dropdown-mobile .inputDiv,
.dropdown-mobile .Secondinput {
  width: 100%;
  margin-bottom: 15px;
  flex-direction: column;
}

.dropdown-mobile .submit {
  width: 100%;
  padding: 10px;
  background-color: var(--color-yellow);
  color: #fff;
  border: none;
  cursor: pointer;
}

.navList li a {
  color: white;
}

.input {
  width: auto;
}

.small-main {
  background: white;
  border-radius: 30px;
  height: 50px;
  width: 100%;
}

.small-main-tab {
  height: 30px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 10px;
}

.small-main-tab button {
  border: none;
  font-size: 16px;
  padding: 12px 4px;
  color: #C4D322;
  border-radius: 30px;
  background-color: white;
  font-weight: 400;
}

/* Enhanced Mobile Navigation Styles */
.mobile-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
}

.mobile-menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInOverlay 0.3s ease-in-out;
}

.mobile-menu-content {
  width: 90%;
  max-width: 400px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(174, 187, 30, 0.2);
  animation: slideInMenu 0.3s ease-out;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
}

.mobile-menu-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.mobile-menu-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-menu-link {
  color: white;
  text-decoration: none;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
}

.mobile-menu-link:hover {
  background-color: rgba(174, 187, 30, 0.1);
  border-color: rgba(174, 187, 30, 0.3);
  transform: translateX(4px);
}

.mobile-menu-cta {
  background: linear-gradient(135deg, #AEBB1E 0%, #D3DC5A 100%);
  color: #000 !important;
  font-weight: 600;
  margin-top: 1rem;
}

.mobile-menu-cta:hover {
  background: linear-gradient(135deg, #D3DC5A 0%, #AEBB1E 100%);
  transform: translateX(0) scale(1.02);
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInMenu {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@media (min-width: 769px) {
  .mobile-menu-button {
    display: none;
  }
}
