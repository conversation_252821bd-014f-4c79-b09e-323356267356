.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100vh;
  overflow: hidden;
  color: var(--color-white);
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.outer-headings,
.hero-para,
.container {
  position: relative;
  z-index: 1;
}

.outer-headings {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.inner-headings {
  border: 0 solid #ddd;
  height: 150px;
  line-height: 150px;
  font-size: 145px;
  text-transform: uppercase;
  overflow: hidden;
  transition: opacity 0.5s ease;
  font-weight: bold;
}

.fade-in {
  opacity: 1;
  animation: popAnimation 0.5s ease;
}

.fade-out {
  opacity: 0.75;
}
.hero-para p {
  color: var(--color-yellow);
  font-size: 25px;
  margin-bottom: 6.1rem;
}

@keyframes popAnimation {
  0%,
  100% {
    transform: scale(1) translateY(0);
  }
  10% {
    transform: scale(1.1) translateY(-5px);
  }
  20% {
    transform: scale(1) translateY(0);
  }
}

.smile {
  width: 2.4rem;
  height: 2.4rem;
  background: 0 0 / 100% repeat-y;
  background-image: url("../../assets/svgs/smile.svg");
  border-radius: 50%;
  position: relative;
  border-radius: 50%;
  transition: background-position var(--tr-main-dur) var(--tr-main-tf);
}
.smile:hover {
  background-position-y: -2.4rem;
}

/* Responsive Scaling - Maintaining Visual Parity */
@media only screen and (max-width: 767px) {
  .hero-section {
    height: 100vh; /* Maintain full height on mobile */
    min-height: 600px;
    padding: 0 clamp(1rem, 4vw, 2rem);
  }

  .outer-headings {
    margin-top: clamp(60px, 12vh, 120px);
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
  }

  .inner-headings {
    height: auto;
    line-height: 1.1;
    font-size: clamp(2.5rem, 12vw, 8rem); /* Proportional scaling */
    margin-top: 0;
    padding: 0;
    text-align: center;
    width: 100%;
  }

  .hero-para {
    width: 100%;
    padding: 0 clamp(1rem, 4vw, 2rem);
    margin-top: clamp(1rem, 4vh, 2rem);
  }

  .hero-para p {
    font-size: clamp(1rem, 4vw, 1.5rem); /* Proportional text scaling */
    margin-bottom: clamp(2rem, 6vh, 4rem);
    line-height: 1.5;
    max-width: 100%;
  }
}

@media only screen and (max-width: 480px) {
  .hero-section {
    height: 100vh; /* Maintain full viewport height */
    min-height: 550px;
  }

  .inner-headings {
    font-size: clamp(2rem, 10vw, 6rem); /* Scaled but proportional */
  }

  .hero-para p {
    font-size: clamp(0.9rem, 3.5vw, 1.25rem);
    line-height: 1.4;
  }
}

/* Tablet responsiveness - Maintain desktop-like proportions */
@media only screen and (min-width: 768px) and (max-width: 1023px) {
  .hero-section {
    height: 100vh;
  }

  .inner-headings {
    font-size: clamp(6rem, 15vw, 12rem); /* Proportional to desktop */
    height: auto;
    line-height: 1.1;
  }

  .hero-para p {
    font-size: clamp(1.25rem, 3vw, 2rem); /* Proportional scaling */
  }
}
