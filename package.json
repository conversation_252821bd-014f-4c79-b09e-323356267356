{"name": "techrypt", "version": "1.0.0", "main": "index.js", "scripts": {"start": "react-scripts start", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "A modern MERN + Flask application that combines AI-powered chatbot functionality with voice commands for seamless appointment scheduling.", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.16.0"}}