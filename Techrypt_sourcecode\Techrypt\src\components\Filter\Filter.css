.filter-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #0f0f0f;
  color: var(--color-white);
  overflow: hidden;
  height: auto;
  padding-bottom: 40px;
}
.filter-section-filter-container {
  width: 85vw;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-h1 {
  font-size: 5rem;
  padding: 40px;
  text-align: center;
}

select {
  width: 220px;
  height: 35px;
  border-radius: 25px;
  border: 2px solid #ccc;
  background-color: transparent;
  color: #ccc;
  padding: 0 5px;
  margin-left: 5px;
  margin-bottom: 10px;
}

/* Enhanced filter dropdown styling for better visibility */
.filter-dropdown {
  width: 220px;
  height: 44px; /* Touch-friendly height */
  border-radius: 25px;
  border: 2px solid #AEBB1E;
  background-color: #2a2a2a;
  color: white !important; /* Force white text */
  padding: 0 15px;
  margin-left: 5px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-dropdown:focus {
  outline: none;
  border-color: #D3DC5A;
  box-shadow: 0 0 0 3px rgba(174, 187, 30, 0.2);
}

.filter-dropdown:hover {
  border-color: #D3DC5A;
  background-color: #3a3a3a;
}

option {
  background-color: #2a2a2a !important;
  color: white !important;
  padding: 8px;
  font-size: 16px;
}
.filter-rows {
  width: 100%;
  height: auto;
}
.filter-rows:hover {
  color: #444;
  border-color: #444;
}

.show-more-container {
  text-align: center;
  margin-top: 20px;
}

.show-more-button {
  background-color: var(--color-yellow); /* Blue background */
  color: var(--color-white); /* White text */
  padding: 10px 20px; /* Padding for the button */
  border: none; /* Remove default border */
  border-radius: 50px; /* Rounded corners */
  font-size: 16px; /* Font size */
  cursor: pointer; /* Pointer cursor on hover */
  transition: background-color 0.3s ease; /* Smooth hover transition */
}

.show-more-button:focus {
  outline: none; /* Remove focus outline */
}

.show-more-button:active {
  background-color: var(--color-yellow); /* Even darker blue on click */
  transform: scale(0.98); /* Slightly reduce size on click */
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .filter-section {
    padding: 20px 16px 40px 16px;
  }

  .filter-section-filter-container {
    width: 95vw;
    gap: 1.5rem;
  }

  .filter-h1 {
    font-size: 2.5rem;
    text-align: center;
    padding: 20px;
  }

  .filter-select {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  select {
    width: 100%;
    height: 44px; /* Touch-friendly height */
    margin-left: 0;
    margin-bottom: 16px;
    padding: 0 16px;
    font-size: 16px; /* Prevent iOS zoom */
  }

  .filter-dropdown {
    width: 100% !important;
    height: 48px !important; /* Extra touch-friendly on mobile */
    margin-left: 0 !important;
    margin-bottom: 16px !important;
    padding: 0 16px !important;
    font-size: 16px !important; /* Prevent iOS zoom */
    border-radius: 12px !important; /* Slightly less rounded on mobile */
  }
}

/* Tablet Responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .filter-section {
    padding: 30px 24px 40px 24px;
  }

  .filter-section-filter-container {
    width: 90vw;
  }

  .filter-h1 {
    font-size: 4rem;
    padding: 30px;
  }

  select {
    width: 200px;
    height: 40px;
  }
}

/* Desktop Responsiveness */
@media (min-width: 1025px) {
  .filter-section {
    padding: 40px 32px 40px 32px;
  }

  .filter-section-filter-container {
    width: 85vw;
    max-width: 1200px;
  }

  .filter-h1 {
    font-size: 5rem;
    padding: 40px;
  }
}
