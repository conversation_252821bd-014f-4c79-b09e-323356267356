@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');
/* @import "tailwindcss"; */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* @tailwind base;
@tailwind components;
@tailwind utilities;
@theme {
  --color-five:#0158A6;
  --color-six:#121212;
  --color-seven:#3D550C;
  --color-eight:#FF56A5;
  --color-nine:#0a0f14;
  --color-ten:#CFE5FF;
  --color-primary:#AEBB1E;
  --color-black: #0f0f0f;
} */
.glowing-yellow{
  filter: drop-shadow(0 0 7px #F5FF1E);
}
.glowing-green{
  filter: drop-shadow(0 0 7px #D4FF00);
}
.glowing-pink{
  filter: drop-shadow(0 0 7px #FF56A5);
}
.glow-green {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-hover:hover {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-pink {
  box-shadow: 
    0 0 35px rgba(255, 86, 165, 0.4), 
    0 0 17px rgba(255, 86, 165, 0.3), 
    0 0 10px rgba(255, 86, 165, 0.2);
}
.fading{
  height: 167px;
  width: 100%;
  z-index: 50;
  margin: -100px 0px 0px 0px;
  position: relative;
  background: linear-gradient(0deg, rgba(15, 15, 15, 1) 50%, rgba(31, 31, 31, 0) 100%);
}
.roboto {
  font-family: "Roboto", sans-serif;
}
.inter {
  font-family: "Inter", sans-serif;
}
.bebas {
  font-family: "Bebas Neue", sans-serif;
}
body{
  scroll-behavior: smooth;
  padding-top: 80px; /* Add space for fixed header */
}
* {
  margin: 0;
  padding: 0;
  font-family: "Right Grotesk", sans-serif;
}

@font-face {
  font-family: "Right Grotesk";
  src: url("/path/to/RightGrotesk-Regular.woff2") format("woff2"),
    url("/path/to/RightGrotesk-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: "Right Grotesk", sans-serif;
}

:root {
  --color-black: #0f0f0f;
  --color-black-2: #0d0d0d;
  --color-white: #fff;
  /*--color-yellow: #aebb1e;
  --color-primary: #AEBB1E;*/;
  --color-yellow: #9cad25;
  /*--color-primary: #9cad25; old main colour*/
  --color-primary: #C4D322;
  --color-blue: #9bd4d7;
  --color-gray-800: #1a1a1a;
  --color-gray-700: #2a2a2a;
  --color-gray-600: #3a3a3a;
  --tr-main-tf: cubic-bezier(0.36, 0.3, 0, 1);
  --tr-main-dur: 300ms;
}

::-webkit-scrollbar {
  width: 12px; 
}

::-webkit-scrollbar-track {
  background-color: black;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary); /* Changed from white to primary color */
  border-radius: 6px;
  border: 2px solid black; /* Added border for a cleaner look */
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-yellow); /* Slightly lighter shade on hover */
  cursor: pointer;
}

html {
  scroll-behavior: smooth;
}

.slide-in {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: bottom;
}

.slide-out {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: top;
}

/* ===== COMPREHENSIVE MOBILE RESPONSIVENESS ===== */

/* Base responsive utilities */
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

/* Responsive text utilities */
.text-responsive-xs { font-size: clamp(0.75rem, 2vw, 0.875rem); }
.text-responsive-sm { font-size: clamp(0.875rem, 2.5vw, 1rem); }
.text-responsive-base { font-size: clamp(1rem, 3vw, 1.125rem); }
.text-responsive-lg { font-size: clamp(1.125rem, 4vw, 1.25rem); }
.text-responsive-xl { font-size: clamp(1.25rem, 5vw, 1.5rem); }
.text-responsive-2xl { font-size: clamp(1.5rem, 6vw, 2rem); }
.text-responsive-3xl { font-size: clamp(1.875rem, 7vw, 2.5rem); }
.text-responsive-4xl { font-size: clamp(2.25rem, 8vw, 3rem); }
.text-responsive-5xl { font-size: clamp(3rem, 10vw, 4rem); }

/* Responsive spacing utilities */
.spacing-responsive-xs { padding: clamp(0.5rem, 2vw, 1rem); }
.spacing-responsive-sm { padding: clamp(1rem, 3vw, 1.5rem); }
.spacing-responsive-md { padding: clamp(1.5rem, 4vw, 2rem); }
.spacing-responsive-lg { padding: clamp(2rem, 5vw, 3rem); }
.spacing-responsive-xl { padding: clamp(3rem, 6vw, 4rem); }

/* Responsive grid utilities */
.grid-responsive-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive-2 { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 768px) {
  .grid-responsive-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 1024px) {
  .grid-responsive-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-responsive-6 { grid-template-columns: repeat(6, 1fr); }
}

/* Responsive flex utilities */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    align-items: center;
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-first button styles */
.btn-responsive {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .btn-responsive {
    padding: 1rem 2rem;
    font-size: 1.125rem;
  }
}

/* Mobile navigation utilities */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* Safe area utilities for mobile devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Responsive image utilities */
.img-responsive {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Mobile-specific overrides */
@media (max-width: 767px) {
  /* Ensure no horizontal scrolling */
  body {
    overflow-x: hidden;
  }

  /* Mobile-friendly spacing */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile text adjustments */
  h1 { font-size: clamp(1.75rem, 6vw, 2.5rem); }
  h2 { font-size: clamp(1.5rem, 5vw, 2rem); }
  h3 { font-size: clamp(1.25rem, 4vw, 1.75rem); }
  h4 { font-size: clamp(1.125rem, 3.5vw, 1.5rem); }
  h5 { font-size: clamp(1rem, 3vw, 1.25rem); }
  h6 { font-size: clamp(0.875rem, 2.5vw, 1rem); }

  /* Mobile-friendly forms */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  /* Mobile-friendly tables */
  table {
    font-size: 0.875rem;
  }

  /* Mobile-friendly cards */
  .card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
  }

  /* Mobile-specific service card enhancements */
  .service-card-responsive {
    min-height: 280px !important;
    height: auto !important;
    padding: 1.5rem !important;
  }

  .service-icon-container {
    height: 60px !important;
    margin-bottom: 1rem !important;
  }

  .service-title {
    font-size: clamp(1.25rem, 4vw, 1.5rem) !important;
    margin: 0.75rem 0 !important;
  }

  .service-description {
    font-size: clamp(0.875rem, 2.5vw, 1rem) !important;
    line-height: 1.5 !important;
  }
}

/* Techrypt Brand Colors */
.bg-techrypt-primary {
  background-color: var(--color-primary);
}

.text-techrypt-primary {
  color: var(--color-primary);
}

.border-techrypt-primary {
  border-color: var(--color-primary);
}

.hover-techrypt-primary:hover {
  background-color: var(--color-primary);
  color: var(--color-black);
}

/* Enhanced Button Styles */
.btn-techrypt {
  background-color: var(--color-primary);
  color: var(--color-black);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-techrypt:hover {
  background-color: #aebb1ee6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px #aebb1e4d;
}

.btn-techrypt-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-techrypt-outline:hover {
  background-color: var(--color-primary);
  color: var(--color-black);
}

/* Loading and Animation Enhancements */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

