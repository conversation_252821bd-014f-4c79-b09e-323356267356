import P1 from "../../assets/Images/animoca.png";
import P2 from "../../assets/Images/nexters.png";
import P3 from "../../assets/Images/pixonic.png";
import sensor from "../../assets/Images/sensorium.png";
import mask from "../../assets/svgs/mask.svg";
import mask1 from "../../assets/svgs/mask1.svg";
import jac from "../../assets/Images/jacqImg.jpg";
import feroze from "../../assets/Images/ferozeImg.jpg";
import Bhatt from "../../assets/Images/maheshImg.jpg";
import sanam from "../../assets/Images/sanam.jpg";
import urwa from "../../assets/Images/urwa.jpg";
import farhan from "../../assets/Images/farhan.jpg";
import bohemia from "../../assets/Images/bohemia.jpg";
import P4 from "../../assets/Images/ferozeImg.jpg";

// VERTICLE

import Yt from "../Images/youtube.png";
import Insta from "../Images/instagram.png";
import Tt from "../Images/tiktok.png";
import Telegram from "../Images/telegram.png";
import twitch from "../Images/twitch.png";
import snap from "../Images/snapchat.png";
import fbg from "../Images/facebookgaming.png";
import ap from "../Images/applepodcasts.png";
import { work1, work2, work3 } from "../mainImages";

export const sliderdata = [
  {
    maskImg: mask,
    logo: P1,
  },
  {
    maskImg: mask1,
    logo: P2,
  },
  {
    maskImg: mask,
    logo: P3,
  },
  {
    maskImg: mask1,
    logo: P4,
  },
  {
    maskImg: mask,
    logo: sensor,
  },
];

export const social = [
  {
    icon: Yt,
  },
  {
    icon: Insta,
  },
  {
    icon: Tt,
  },
  {
    icon: Telegram,
  },
  {
    icon: twitch,
  },
  {
    icon: snap,
  },
  {
    icon: fbg,
  },
  {
    icon: ap,
  },
];

export const verticals = [
  {
    text: "E-commerce",
  },
  {
    text: "market",
  },
  {
    text: "Education",
  },
  {
    text: "Travel",
  },
  {
    text: "Visa",
  },
  {
    text: "Utilities",
  },
  {
    text: "Social",
  },
  {
    text: "Health",
  },
  {
    text: "Food & Drinks",
  },
  {
    text: "Entertainment",
  },
  {
    text: "Delivery",
  },
  {
    text: "Marketing",
  },
  {
    text: "Telecommunications",
  },
];

export const data = [
  {
    Company: "BBQ Barn",
    Services: "AI Chatbot Integration, Social Media Automation, Performance Marketing, Website Development",
    GEO: "North America",
    Platform: "Instagram, TikTok",
    Image: "/Images/clients/bbqbarn.jpg",
    Vertical: "Food & Drinks",
    Results:
      "Our 360° marketing campaign for BBQ Barn resulted in a 300% sales increase, fueled by strategic influencer partnerships, user-generated content, and seamless chatbot integration on their website for enhanced customer engagement. The #BBQBarnBiteChallenge TikTok campaign achieved over 18 million views with a 32% engagement rate among Gen Z, while geo-targeted ads in 15 major cities drove 47,200 store visits, tracked via QR code redemptions.",
  },
  {
    Company: "Bolt E-Sim",
    Services: "AI Chatbot Integration, Ecommerce Store, Performance Marketing, Social Media Automation",
    GEO: "WorldWide",
    Platform: "TikTok, Instagram Reels",
    Image: "/Images/clients/bolt.jpg",
    Vertical: "Telecommunications",
    Results:
      "Our comprehensive campaign for Bolt E-Sim, featuring Chatbot Integration, Shopify Store optimization, Meta Ads Funneling, and Social Media Automation, achieved remarkable results across 15 global markets via TikTok and Instagram Reels. The initiative drove over 25 million views with a 28% engagement rate, enhanced by automated social content and chatbot-driven customer interactions. This led to a 220% surge in app downloads and Shopify store sales, while targeted Meta ads funneled 35,000 new user sign-ups, tracked via unique referral codes.",
  },
  {
    Company: "Echoverse",
    Services: "Social Media Automation, Website Development, Performance Marketing, Social Media Automation",
    GEO: "North America",
    Platform: "Meta, YouTube, Spotify",
    Image: "/Images/clients/echoverse.jpg",
    Vertical: "Entertainment",
    Results:
      "Our multifaceted campaign for Echoverse, incorporating Social Media Automation, Website Development, Meta Ads Funneling, and YouTube Automation, delivered outstanding results across the United States on Meta, YouTube, and Spotify platforms. The strategy generated over 30 million impressions with a 25% engagement rate, driven by automated content and targeted Meta ads, while the newly developed website saw a 180% increase in traffic. YouTube Automation boosted video views by 15 million, and Spotify ad campaigns contributed to 20,000 new user subscriptions, tracked via unique promo codes.",
  },
  {
    Company: "Jazzy Barbershop",
    Services: "Social Media Automation, Performance Marketing",
    GEO: "Asia",
    Platform: "Meta, YouTube, Spotify",
    Image: "/Images/clients/jazzybarbershop.jpg",
    Vertical: "Health",
    Results:
      "Our dynamic campaign for Jazzy Barbershop, blending Social Media Automation with a slick Meta Ads Funneling strategy, turned heads across Asia on Meta, YouTube, and Spotify. This bold move racked up over 12 million eye-catching impressions, sparking a 30% engagement buzz with automated posts and pinpointed ad drops. The result? A jaw-dropping 150% spike in appointment bookings, while YouTube and Spotify hype fueled 8,000 fresh client sign-ups, all tracked with exclusive promo codes that kept the momentum rolling.",
  },
  {
    Company: "Montoya Crew",
    Services: "Website Development, Performance Marketing",
    GEO: "North America",
    Platform: "Meta, Shopify",
    Image: "/Images/clients/montoyacrew.jpg",
    Vertical: "E-commerce",
    Results:
      "Our tailored campaign for Montoya Crew, blending Website Development with Meta Ads Funneling, elevated the ecommerce landscape across the United States on Meta and Shopify platforms. This strategic effort delivered over 18 million striking impressions, igniting a 27% engagement surge with a sleek new website and precision-targeted ads. The result was a robust 200% jump in online sales, while Shopify integrations drove 12,000 new customer registrations, tracked through exclusive discount codes.",
  },
  {
    Company: "PRX",
    Services: "AI Chatbot Integration, Performance Marketing",
    GEO: "North America",
    Platform: "Meta",
    Image: "/Images/clients/prx.jpg",
    Vertical: "Visa",
    Results:
      "PRX's campaign with AI Booking Agent Integration and Meta Ads Funneling transformed visa applications in the US on Meta, generating 14M impressions, 25% engagement, and a 160% rise in submissions, with 9,000 bookings tracked via AI links.",
  },
  {
    Company: "Rack's Gang Apparel",
    Services: "Ecommerce Store",
    GEO: "North America",
    Platform: "Shopify",
    Image: "/Images/clients/racksgangapparel.jpg",
    Vertical: "E-commerce",
    Results:
      "Rack's Gang Apparel's E-commerce Store Setup on Shopify revolutionized their US presence, delivering 12M impressions, a 24% engagement rate, and a 150% sales increase, with 8,000 new customer sign-ups tracked via exclusive promo codes.",
  },
  {
    Company: "Roof to Basement Renovations",
    Services: "AI Chatbot Integration, Social Media Automation, Performance Marketing, Website Development",
    GEO: "North America",
    Platform: "Meta, Instagram",
    Image: "/Images/clients/rooftobasement.jpg",
    Vertical: "Utilities",
    Results:
      "Roof to Basement Renovations' campaign, featuring Integrated Booking Agent, Social Media Management, Meta Ads Funneling, and Website Development, enhanced their US utilities presence on Meta and Instagram, generating 15M impressions, a 26% engagement rate, and a 170% inquiry surge, with 10,000 bookings tracked via unique links.",
  },
  {
    Company: "Simon Nicholas Real Estate Agency",
    Services: "AI Chatbot Integration, Social Media Automation, Performance Marketing, Website Development",
    GEO: "North America",
    Platform: "Meta, YouTube, Instagram",
    Image: "/Images/clients/simonnicholas.jpg",
    Vertical: "Retail",
    Results:
      "Simon Nicholas Real Estate Agency's campaign, combining Integrated Booking Agent, Social Media Management, Meta Ads Funneling, and Website Development, boosted their US retail presence on Meta, YouTube, and Instagram, achieving 18M impressions, a 27% engagement rate, and a 180% inquiry increase, with 12,000 bookings tracked via unique links.",
  },
  {
    Company: "Tooth Mechanic",
    Services: "AI Chatbot Integration, Social Media Automation, Performance Marketing, Website Development",
    GEO: "North America",
    Platform: "Meta, YouTube, Instagram",
    Image: "/Images/clients/toothmechanic.jpg",
    Vertical: "Health",
    Results:
      "Tooth Mechanic's campaign, integrating Booking Agent, Social Media Management, Meta Ads Funneling, and Website Development, elevated their US health presence on Meta, YouTube, and Instagram, delivering 16M impressions, a 26% engagement rate, and a 170% booking surge, with 11,000 appointments tracked via unique links.",
  }
];