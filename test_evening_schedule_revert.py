#!/usr/bin/env python3
"""
Test script to verify the reverted evening/overnight business hours schedule
"""

import requests
import json
from datetime import datetime, timedelta

def test_evening_schedule():
    """Test the reverted evening/overnight business hours"""
    print('🌙 TESTING REVERTED EVENING/OVERNIGHT BUSINESS HOURS')
    print('=' * 70)
    
    # Check if Flask server is running
    try:
        health_response = requests.get('http://localhost:5000/health', timeout=5)
        if health_response.status_code == 200:
            print('✅ Flask server is running')
        else:
            print('❌ Flask server health check failed')
            return False
    except Exception as e:
        print(f'❌ Flask server not accessible: {e}')
        return False
    
    print()
    
    # Test 1: Monday evening appointment (should work - 6 PM - 3 AM)
    print('🌙 TEST 1: Monday Evening Appointment (6 PM - 3 AM PKT)')
    print('-' * 50)
    
    # Find next Monday
    next_monday = datetime.now() + timedelta(days=1)
    while next_monday.weekday() != 0:  # 0 = Monday
        next_monday += timedelta(days=1)
    monday_date = next_monday.strftime('%Y-%m-%d')
    
    monday_evening_appointment = {
        'name': 'Monday Evening Test User',
        'email': '<EMAIL>',
        'phone': '+**********',
        'services': ['Website Development'],
        'preferred_date': monday_date,
        'preferred_time': '20:00',  # 8 PM PKT - within evening business hours
        'preferred_time_local': '20:00',
        'user_timezone': 'Asia/Karachi',
        'notes': 'Testing Monday evening business hours',
        'status': 'Pending',
        'created_at': datetime.now().isoformat(),
        'source': 'monday_evening_test'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/appointment',
            json=monday_evening_appointment,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f'✅ Monday evening appointment created: {result.get("appointment_id")}')
            print(f'📧 Emails should be sent for Monday evening appointment')
        else:
            print(f'❌ Monday evening appointment failed: {response.status_code} - {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing Monday evening appointment: {e}')
    
    print()
    
    # Test 2: Monday late night appointment (should work - 1 AM next day)
    print('🌙 TEST 2: Monday Late Night Appointment (1 AM next day)')
    print('-' * 50)
    
    monday_late_appointment = {
        'name': 'Monday Late Night Test User',
        'email': '<EMAIL>',
        'phone': '+1555333444',
        'services': ['Branding Services'],
        'preferred_date': monday_date,
        'preferred_time': '01:00',  # 1 AM PKT - within overnight business hours
        'preferred_time_local': '01:00',
        'user_timezone': 'Asia/Karachi',
        'notes': 'Testing Monday late night business hours',
        'status': 'Pending',
        'created_at': datetime.now().isoformat(),
        'source': 'monday_late_test'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/appointment',
            json=monday_late_appointment,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f'✅ Monday late night appointment created: {result.get("appointment_id")}')
            print(f'📧 Emails should be sent for Monday late night appointment')
        else:
            print(f'❌ Monday late night appointment failed: {response.status_code} - {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing Monday late night appointment: {e}')
    
    print()
    
    # Test 3: Saturday evening appointment (should work - 6 PM - 10 PM)
    print('🌙 TEST 3: Saturday Evening Appointment (6 PM - 10 PM PKT)')
    print('-' * 50)
    
    # Find next Saturday
    next_saturday = datetime.now() + timedelta(days=1)
    while next_saturday.weekday() != 5:  # 5 = Saturday
        next_saturday += timedelta(days=1)
    saturday_date = next_saturday.strftime('%Y-%m-%d')
    
    saturday_evening_appointment = {
        'name': 'Saturday Evening Test User',
        'email': '<EMAIL>',
        'phone': '+1555555666',
        'services': ['Chatbot Development'],
        'preferred_date': saturday_date,
        'preferred_time': '19:00',  # 7 PM PKT - within Saturday evening hours
        'preferred_time_local': '19:00',
        'user_timezone': 'Asia/Karachi',
        'notes': 'Testing Saturday evening business hours',
        'status': 'Pending',
        'created_at': datetime.now().isoformat(),
        'source': 'saturday_evening_test'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/appointment',
            json=saturday_evening_appointment,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f'✅ Saturday evening appointment created: {result.get("appointment_id")}')
            print(f'📧 Emails should be sent for Saturday evening appointment')
        else:
            print(f'❌ Saturday evening appointment failed: {response.status_code} - {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing Saturday evening appointment: {e}')
    
    print()
    
    # Test 4: Monday afternoon appointment (should be rejected - outside hours)
    print('🌙 TEST 4: Monday Afternoon Appointment (Should be rejected - 2 PM)')
    print('-' * 50)
    
    monday_afternoon_appointment = {
        'name': 'Monday Afternoon Test User',
        'email': '<EMAIL>',
        'phone': '+1555777888',
        'services': ['Website Development'],
        'preferred_date': monday_date,
        'preferred_time': '14:00',  # 2 PM PKT - outside evening business hours
        'preferred_time_local': '14:00',
        'user_timezone': 'Asia/Karachi',
        'notes': 'Testing Monday afternoon rejection',
        'status': 'Pending',
        'created_at': datetime.now().isoformat(),
        'source': 'monday_afternoon_test'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/appointment',
            json=monday_afternoon_appointment,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 400:
            print(f'✅ Monday afternoon appointment correctly rejected: {response.status_code}')
            print(f'📧 No emails should be sent for rejected afternoon appointment')
        elif response.status_code == 200:
            print(f'⚠️ Monday afternoon appointment was accepted (unexpected): {response.status_code}')
            result = response.json()
            print(f'🆔 Appointment ID: {result.get("appointment_id")}')
        else:
            print(f'❌ Unexpected response for Monday afternoon: {response.status_code} - {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing Monday afternoon appointment: {e}')
    
    print()
    
    # Test 5: Conflict scenario with evening schedule
    print('🌙 TEST 5: Conflict Scenario (Evening Schedule)')
    print('-' * 50)
    
    # Try to book the same Monday evening time slot again
    conflict_appointment = monday_evening_appointment.copy()
    conflict_appointment['name'] = 'Conflict Evening Test User'
    conflict_appointment['email'] = '<EMAIL>'
    conflict_appointment['source'] = 'conflict_evening_test'
    conflict_appointment['notes'] = 'Testing conflict detection with evening schedule'
    
    try:
        response = requests.post(
            'http://localhost:5000/appointment',
            json=conflict_appointment,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 409:
            result = response.json()
            print(f'✅ Evening conflict detected correctly: {response.status_code}')
            print(f'📋 Conflict message: {result.get("message", "No message")}')
            
            if result.get("suggested_slot"):
                suggested = result.get("suggested_slot")
                print(f'💡 Suggested alternative: {suggested.get("date")} at {suggested.get("time")}')
                print(f'📧 No emails should be sent for conflicted appointment')
                print(f'🎯 Frontend should show conflict modal with suggested evening time')
            else:
                print(f'⚠️ No suggested slot provided')
        else:
            print(f'❌ Expected conflict (409) but got: {response.status_code}')
            print(f'Response: {response.text}')
            
    except Exception as e:
        print(f'❌ Error testing evening conflict scenario: {e}')
    
    return True

def main():
    """Main test function"""
    print('🚀 EVENING/OVERNIGHT SCHEDULE REVERT TEST')
    print('Testing reverted business hours: Mon-Fri 6PM-3AM, Sat 6PM-10PM, Sun Closed')
    print('=' * 80)
    
    success = test_evening_schedule()
    
    print()
    print('🎯 TEST SUMMARY')
    print('=' * 40)
    
    if success:
        print('✅ ALL TESTS COMPLETED')
        print()
        print('🔧 REVERTED BUSINESS HOURS:')
        print('1. ✅ Monday-Friday: 6:00 PM - 3:00 AM (next day) PKT')
        print('2. ✅ Saturday: 6:00 PM - 10:00 PM PKT')
        print('3. ✅ Sunday: Closed (no changes)')
        print()
        print('🔧 BACKEND FIXES APPLIED:')
        print('• ✅ Updated _is_business_hours() for evening/overnight schedule')
        print('• ✅ Fixed _find_next_available_slot() for evening start times')
        print('• ✅ Updated business hours display in error messages')
        print('• ✅ Maintained real-time database conflict checking')
        print()
        print('🔧 FRONTEND FIXES APPLIED:')
        print('• ✅ Reverted getLocalBusinessHours() to evening schedule')
        print('• ✅ Fixed generateTimeSlots() for overnight schedule')
        print('• ✅ Updated business hours display in appointment form')
        print('• ✅ Maintained conflict modal functionality')
        print()
        print('🎯 EXPECTED BEHAVIOR:')
        print('• Evening time slots (6 PM onwards) for weekdays and Saturday')
        print('• Overnight slots (until 3 AM) for Monday-Friday')
        print('• Real-time conflict detection with suggested alternatives')
        print('• Proper timezone handling for evening/overnight schedule')
        print('• Three-email notification system for successful appointments')
    else:
        print('❌ SOME TESTS FAILED')
        print('💡 Check Flask server and try again')
    
    print()
    print('📝 NEXT STEPS:')
    print('1. Test with actual TechryptChatbot.jsx frontend')
    print('2. Verify evening time slot generation for different dates')
    print('3. Test overnight schedule (6 PM to 3 AM) functionality')
    print('4. Confirm conflict modal works with evening appointments')
    print('5. Verify timezone conversions for evening/overnight hours')

if __name__ == "__main__":
    main()
